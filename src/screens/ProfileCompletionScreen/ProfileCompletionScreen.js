import React, { useRef, useState, useEffect, useCallback } from "react";
import {
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  View,
  Alert,
} from "react-native";
import PropTypes from "prop-types";
import { useDispatch, useSelector } from "react-redux";
import {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from "react-native-reanimated";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import { PrimaryButton } from "../../components/Button";
import { styles } from "./styles";
import { MAIN_SECTIONS, INITIAL_FORM_STATE } from "./constants/formConfig";
import PersonalSection from "./components/PersonalSection";
import SocialSection from "./components/SocialSection";
import BusinessSection from "./components/BusinessSection";
import TabNavigation from "./components/TabNavigation";
import {
  getAccessProfileFromProfileId,
  getPersonalProfile,
  updateBusinessDetailsSharingProfile,
  updateSharingProfile,
} from "../../redux/features/SharingProfileSlice";
import { mapProfileDataForApi } from "../../utils/profileDataMapper";
import { showToast } from "../../utils/toastConfig";
import ProfilePicture from "../../components/ProfilePicture";

const ProfileCompletionScreen = ({ navigation, route }) => {
  const { profileId, ownProfileData } = route?.params || {};
  const dispatch = useDispatch();
  const user = useSelector((state) => state.auth.user);
  const accessProfileFromProfileIdState = useSelector(
    (state) => state.sharingProfileSlice.getAccessProfileFromProfileId
  );
  const { data: accessProfileData } = accessProfileFromProfileIdState;

  // Refs
  const tabPosition = useSharedValue(0);
  const phoneInputRef = useRef(null);
  const scrollViewRef = useRef(null);
  const personalSectionRef = useRef(null);
  const socialSectionRef = useRef(null);
  const businessSectionRef = useRef(null);

  // State
  const [activeMainSection, setActiveMainSection] = useState("personal");
  const [form, setForm] = useState(INITIAL_FORM_STATE);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [isTimezoneDropdownOpen, setIsTimezoneDropdownOpen] = useState(false);
  // New: additional emails and phones state
  const [additionalEmails, setAdditionalEmails] = useState([]);
  const [additionalPhones, setAdditionalPhones] = useState([
    { number: "", countryCode: "+1" },
  ]);

  // Fetch profile data on mount
  useEffect(() => {
    if (profileId) {
      dispatch(getAccessProfileFromProfileId(profileId));
    }
  }, [dispatch, profileId]);

  const populateFormFromUserInfo = (userInfo, prevForm) => {
    console.log("🚀 ~ populateFormFromUserInfo ~ userInfo:", userInfo);

    const newForm = { ...prevForm };
    // Personal Details
    newForm.firstName = userInfo.firstName || "";
    newForm.middleName = userInfo.middleName || "";
    newForm.lastName = userInfo.lastName || "";
    newForm.nickname = userInfo.nickname || [""];
    newForm.profileUrl = userInfo.profile_image || "";
    newForm.email = userInfo.emails?.[0]?.address || "";
    newForm.phone = userInfo.phoneNumbers?.[0]?.number || "";
    newForm.dateOfBirth = userInfo.dateOfBirth || "";
    newForm.gender = userInfo.gender || "";
    newForm.nationality = userInfo.nationality || "";
    newForm.maritalStatus = userInfo.maritalStatus || "";
    newForm.spouseName = userInfo.spouseName || "";
    newForm.profilePicture = userInfo.profilePicture || "";
    // childrenNames
    newForm.childrenNames = userInfo.childrenNames || [""];
    // hobbies
    newForm.hobbies = userInfo.hobbies || [""];
    // religion
    newForm.religion = userInfo.religion || [""];
    // nativeLanguage
    newForm.nativeLanguage = userInfo.nativeLanguage || "";
    // otherLanguages
    newForm.otherLanguages = userInfo.otherLanguages || [""];
    // notes
    newForm.notes = userInfo.notes || [""];
    // other adresses
    newForm.otherAddresses = userInfo.addresses?.map((address) => {
      return {
        apartment: address.apartment || "",
        street: address.street || "",
        city: address.city || "",
        state: address.state || "",
        zipCode: address.postalCode || "",
        country: address.country || "",
      };
    }) || [
      {
        apartment: "",
        street: "",
        city: "",
        state: "",
        postalCode: "",
        country: "",
      },
    ];
    // billingAddresses
    newForm.billingAddresses = userInfo.billing_address || [
      {
        apartment: "",
        street: "",
        city: "",
        state: "",
        postalCode: "",
        country: "",
      },
    ];
    // cardDetails
    newForm.cardDetails = userInfo.card_details?.map((card) => {
      return {
        cardName: card.nameOnCard || "",
        cardNumber: card.cardNumber || "",
        cardExpiry: card.expiryDate || "",
        cardCvv: card.cvv || "",
      };
    }) || [
      {
        cardName: "",
        cardNumber: "",
        cardExpiry: "",
        cardCvv: "",
      },
    ];
    // accountDetails
    newForm.accountDetails = userInfo.account_details || {
      name: "",
      bank: "",
      accountNumber: "",
      ifscCode: "",
    };
    // paypalDetails
    newForm.paypalDetails = userInfo.payPal_account || [
      {
        paypalEmail: "",
        paypalLink: "",
      },
    ];

    // cryptoWalletDetails
    newForm.cryptoWalletDetails = userInfo.crypto_wallet_details || [
      {
        walletAddress: "",
        walletType: "",
      },
    ];

    // Health Insurance
    newForm.policyNumber = userInfo.healthInsurance?.policyNumber || "";
    newForm.insuranceProvider =
      userInfo.healthInsurance?.insuranceProvider || "";
    newForm.policyPeriod =
      userInfo.healthInsurance?.policyPeriod?.toString() || "";
    newForm.effectiveDate = userInfo.healthInsurance?.effectiveDate || "";
    newForm.expirationDate = userInfo.healthInsurance?.expirationDate || "";
    newForm.sumInsured = userInfo.healthInsurance?.sumInsured?.toString() || "";

    // // Address
    newForm.street = userInfo.addresses_home?.street || "";
    newForm.city = userInfo.addresses_home?.city || "";
    newForm.state = userInfo.addresses_home?.state || "";
    newForm.zipCode = userInfo.addresses_home?.postalCode || "";
    newForm.country = userInfo.addresses_home?.country || "";
    newForm.apartment = userInfo.addresses_home?.apartment || "";

    // // Billing Address
    // newForm.billingApartment = userInfo.billing_address?.apartment || "";
    // newForm.billingStreet = userInfo.billing_address?.street || "";
    // newForm.billingCity = userInfo.billing_address?.city || "";
    // newForm.billingState = userInfo.billing_address?.state || "";
    // newForm.billingZipCode = userInfo.billing_address?.zipCode || "";
    // newForm.billingCountry = userInfo.billing_address?.country || "";

    // Card Details
    // newForm.cardName = userInfo.card_details?.nameOnCard || "";
    // newForm.cardNumber = userInfo.card_details?.cardNumber || "";
    // newForm.cardExpiry = userInfo.card_details?.expiryDate || "";
    // newForm.cardCvv = userInfo.card_details?.cvv || "";

    // // Bank Account
    // newForm.accountName = userInfo.account_details?.name || "";
    // newForm.accountNumber = userInfo.account_details?.accountNumber || "";
    // newForm.ifscCode = userInfo.account_details?.ifscCode || "";
    // newForm.bankName = userInfo.account_details?.bank || "";

    // // Other Address
    // newForm.otherApartment = userInfo.addresses_other?.apartment || "";
    // newForm.otherStreet = userInfo.addresses_other?.street || "";
    // newForm.otherCity = userInfo.addresses_other?.city || "";
    // newForm.otherState = userInfo.addresses_other?.state || "";
    // newForm.otherZipCode = userInfo.addresses_other?.zipCode || "";
    // newForm.otherCountry = userInfo.addresses_other?.country || "";

    // Emergency Contact
    newForm.emergencyContactName =
      userInfo.emergency_contact?.contactName || "";
    newForm.emergencyContactRelationship =
      userInfo.emergency_contact?.relationship || "";
    newForm.emergencyEmail = userInfo.emergency_contact?.email || "";
    newForm.emergencyPhone = userInfo.emergency_contact?.phoneNumber || "";
    newForm.personalWebsite = userInfo.personalWebsite || "";
    newForm.emergencyHobbies = userInfo.hobbies?.join(", ") || "";
    newForm.emergencyReligion = userInfo.religion || "";
    newForm.preferredContactMethod = userInfo.preferredContactMethod || "";
    newForm.timezone = userInfo.timeZone || "";

    // Social Media
    newForm.personalWebsite = userInfo.personalWebsite || [""];
    const social = userInfo?.socialMedia || {};
    newForm.linkedIn = social.linkedin?.map((url) => url.url) || [""];
    newForm.twitter = social.twitter?.map((url) => url.url) || [""];
    newForm.facebook = social.facebook?.map((url) => url.url) || [""];
    newForm.instagram = social.instagram?.map((url) => url.url) || [""];
    newForm.snapchat = social.snapchat?.map((url) => url.url) || [""];
    newForm.whatsapp = social.whatsapp?.map((url) => url.url) || [""];
    newForm.telegram = social.telegram?.map((url) => url.url) || [""];
    newForm.signal = social.signal?.map((url) => url.url) || [""];
    newForm.youtube = social.youtube?.map((url) => url.url) || [""];
    newForm.tiktok = social.tiktok?.map((url) => url.url) || [""];
    newForm.twitch = social.twitch?.map((url) => url.url) || [""];
    newForm.discord = social.discord?.map((url) => url.url) || [""];
    newForm.googleChat = social.googleChat?.map((url) => url.url) || [""];
    newForm.iMessage = social.iMessage?.map((url) => url.url) || [""];
    newForm.wechat = social.wechat?.map((url) => url.url) || [""];
    newForm.kik = social.kik?.map((url) => url.url) || [""];
    newForm.slack = social.slack?.map((url) => url.url) || [""];
    newForm.line = social.line?.map((url) => url.url) || [""];
    newForm.skype = social.skype?.map((url) => url.url) || [""];
    newForm.companyInfo = [
      {
        companyName:
          userInfo?.business_details?.companyInformation?.company_name || "",
        companyLogo:
          userInfo?.business_details?.companyInformation?.company_logo || "",
        companyWebsite:
          userInfo?.business_details?.companyInformation?.website || "",
        companyPhoneNumbers:
          userInfo?.business_details?.companyInformation?.phone?.map(
            (phone) => phone.number
          ) || [""],
        companyEmails:
          userInfo?.business_details?.companyInformation?.email?.map(
            (address) => address.address
          ) || [""],
        companyFaxNumbers:
          userInfo?.business_details?.companyInformation?.fax?.map(
            (fax) => fax.number
          ) || [""],
        companyAddresses: userInfo?.business_details?.business_address?.map(
          (addr) => ({
            street: addr.street || "",
            city: addr.city || "",
            state: addr.state || "",
            postalCode: addr.postalCode || "",
            country: addr.country || "",
          })
        ) || [
          {
            street: "",
            city: "",
            state: "",
            postalCode: "",
            country: "",
          },
        ],
        companyBillingAddresses:
          userInfo?.business_details?.billing_address?.map((addr) => ({
            street: addr.street || "",
            city: addr.city || "",
            state: addr.state || "",
            postalCode: addr.postalCode || "",
            country: addr.country || "",
          })) || [
            {
              street: "",
              city: "",
              state: "",
              postalCode: "",
              country: "",
            },
          ],
        cardDetails: userInfo?.business_details?.card_details?.map((card) => ({
          cardName: card.nameOnCard || "",
          cardNumber: card.cardNumber || "",
          cardExpiry: card.expiryDate || "",
          cardCvv: card.cvv || "",
        })) || [
          {
            cardName: "",
            cardNumber: "",
            cardExpiry: "",
            cardCvv: "",
          },
        ],
        bankAccountInfo: userInfo?.business_details?.account_details?.map(
          (acc) => ({
            accountName: acc.name || "",
            accountNumber: acc.accountNumber || "",
            ifscCode: acc.ifscCode || "",
            bankName: acc.bank || "",
          })
        ) || [
          {
            accountName: "",
            accountNumber: "",
            ifscCode: "",
            bankName: "",
          },
        ],

        companySocialMedia: {
          facebook:
            userInfo?.business_details?.companySocialMedia?.facebook?.map(
              (url) => url.url
            ) || [""],
          instagram:
            userInfo?.business_details?.companySocialMedia?.instagram?.map(
              (url) => url.url
            ) || [""],
          twitter: userInfo?.business_details?.companySocialMedia?.twitter?.map(
            (url) => url.url
          ) || [""],
          snapchat:
            userInfo?.business_details?.companySocialMedia?.snapchat?.map(
              (url) => url.url
            ) || [""],
          whatsapp:
            userInfo?.business_details?.companySocialMedia?.whatsapp?.map(
              (url) => url.url
            ) || [""],
          telegram:
            userInfo?.business_details?.companySocialMedia?.telegram?.map(
              (url) => url.url
            ) || [""],
          signal: userInfo?.business_details?.companySocialMedia?.signal?.map(
            (url) => url.url
          ) || [""],
          skype: userInfo?.business_details?.companySocialMedia?.skype?.map(
            (url) => url.url
          ) || [""],
          youtube: userInfo?.business_details?.companySocialMedia?.youtube?.map(
            (url) => url.url
          ) || [""],
          twitch: userInfo?.business_details?.companySocialMedia?.twitch?.map(
            (url) => url.url
          ) || [""],
          tiktok: userInfo?.business_details?.companySocialMedia?.tiktok?.map(
            (url) => url.url
          ) || [""],
          linkedIn:
            userInfo?.business_details?.companySocialMedia?.linkedin?.map(
              (url) => url.url
            ) || [""],
          iMessage:
            userInfo?.business_details?.companySocialMedia?.iMessage?.map(
              (url) => url.url
            ) || [""],
          googleChat:
            userInfo?.business_details?.companySocialMedia?.googleChat?.map(
              (url) => url.url
            ) || [""],
          discord: userInfo?.business_details?.companySocialMedia?.discord?.map(
            (url) => url.url
          ) || [""],
          wechat: userInfo?.business_details?.companySocialMedia?.wechat?.map(
            (url) => url.url
          ) || [""],
          kik: userInfo?.business_details?.companySocialMedia?.kik?.map(
            (url) => url.url
          ) || [""],
          line: userInfo?.business_details?.companySocialMedia?.line?.map(
            (url) => url.url
          ) || [""],
        },
      },
    ];

    // Business Info
    const biz = userInfo.business_details || {};
    newForm.jobTitle = biz?.jobTitle || "";
    newForm.company = biz?.companyInformation?.company_name || "";
    newForm.department = biz?.department || "";
    newForm.workEmail = biz?.workContact?.email || "";
    newForm.workPhone = biz?.workContact?.phoneNumber || "";
    newForm.workFax = biz?.workContact?.fax || "";
    newForm.resume = biz?.resume || "";
    newForm.certifications = biz?.certifications || [""];
    newForm.industry = biz?.industry || "";
    newForm.workHours = biz?.workSchedule || "";
    newForm.companyWebsite = biz?.companyWebsite || "";
    newForm.businessLinkedIn = biz?.linkedinProfile || "";
    newForm.businessTwitter = biz?.xProfile || "";
    newForm.professionalNotes = biz?.notes || [""];
    newForm.companyLogo = biz?.companyLogo || "";

    // Business Address
    // const bizAddr = biz.business_address || {};
    // newForm.officeBuilding = bizAddr.officeBuilding || "";
    // newForm.businessStreet = bizAddr.street || "";
    // newForm.businessCity = bizAddr.city || "";
    // newForm.businessState = bizAddr.state || "";
    // newForm.businessZipCode = bizAddr.zipCode || "";
    // newForm.businessCountry = bizAddr.country || "";

    // Business Details

    return newForm;
  };

  // Set form from profile data
  useEffect(() => {
    if (accessProfileFromProfileIdState.error) {
      showToast(
        "error",
        "There was an error loading your profile data. You can continue filling out the form, and we'll save your changes."
      );
      return;
    }
    const userInfo = ownProfileData || accessProfileData?.data?.userInfo;

    if (userInfo && typeof userInfo === "object") {
      try {
        setForm((prevForm) => {
          const updatedForm = populateFormFromUserInfo(userInfo, prevForm);
          // preserve checkbox state
          updatedForm.isSameBillingAddress = prevForm.isSameBillingAddress;
          updatedForm.isSameAddress = prevForm.isSameAddress;
          return updatedForm;
        });

        // Set additional emails/phones
        setAdditionalEmails(
          Array.isArray(userInfo.emails)
            ? userInfo.emails.slice(1).map((e) => e.address || "")
            : []
        );

        setAdditionalPhones(
          Array.isArray(userInfo.phoneNumbers)
            ? userInfo.phoneNumbers.slice(1).map((p) => p.number || "")
            : []
        );
      } catch (error) {
        console.error("Error setting profile data:", error);
        Alert.alert(
          "Error",
          "There was an error loading your profile data. Please try refreshing the page."
        );
      }
    }
  }, [accessProfileData, accessProfileFromProfileIdState.error]);

  // Handlers
  const handleChange = useCallback(
    (field, value) => {
      setForm((prev) => {
        if (field.includes(".")) {
          const parts = field.split(".");
          const newForm = { ...prev };
          let current = newForm;
          for (let i = 0; i < parts.length - 1; i++) {
            current[parts[i]] = { ...current[parts[i]] };
            current = current[parts[i]];
          }
          current[parts[parts.length - 1]] = value;
          return newForm;
        }
        return { ...prev, [field]: value };
      });
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: "" }));
      }
    },
    [errors]
  );

  const handleTabPress = useCallback(
    (sectionId, index) => {
      tabPosition.value = withSpring(index * styles.tabButton.width, {
        damping: 15,
        stiffness: 120,
        mass: 1.4,
      });
      setActiveMainSection(sectionId);
    },
    [tabPosition]
  );

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: tabPosition.value }],
  }));

  const mapVisibilitySettingsToAPI = useCallback((visibilitySettings) => {
    return {
      // Basic personal information
      firstName_boolean: visibilitySettings.firstName ?? true,
      middleName_boolean: visibilitySettings.middleName ?? true,
      lastName_boolean: visibilitySettings.lastName ?? true,
      nickname_boolean: [visibilitySettings.nickname ?? true],
      dateOfBirth_boolean: visibilitySettings.dateOfBirth ?? true,
      gender_boolean: visibilitySettings.gender ?? true,
      nationality_boolean: visibilitySettings.nationality ?? true,
      maritalStatus_boolean: visibilitySettings.maritalStatus ?? true,
      profile_image_boolean: visibilitySettings.profileImage ?? true,

      // Contact preferences
      timeZone_boolean: visibilitySettings.timezone ?? true,
      preferredContactMethod_boolean:
        visibilitySettings.preferredContactMethod ?? true,

      // Personal details arrays
      hobbies_boolean: [visibilitySettings.hobbies ?? true],
      religion_boolean: [visibilitySettings.religion ?? true],
      languages_boolean: [visibilitySettings.otherLanguages ?? true],
      nativeLanguage_boolean: visibilitySettings.nativeLanguage ?? true,
      personalWebsite_boolean: [visibilitySettings.personalWebsite ?? true],
      notes_boolean: [true], // Default to true as it's not in form
      childrenName_boolean: [true], // Default to true as it's not in form

      // Address information
      addresses_home_boolean: {
        apartment_boolean: visibilitySettings.apartment ?? true,
        street_boolean: visibilitySettings.street ?? true,
        city_boolean: visibilitySettings.city ?? true,
        state_boolean: visibilitySettings.state ?? true,
        postalCode_boolean: visibilitySettings.zipCode ?? true,
        country_boolean: visibilitySettings.country ?? true,
      },

      // Emergency contact
      emergency_contact_boolean: {
        contactName_boolean: visibilitySettings.emergencyContactName ?? true,
        phoneNumber_boolean: visibilitySettings.emergencyPhone ?? true,
        phoneNumber_country_code_boolean: true, // Default to true
        relationship_boolean:
          visibilitySettings.emergencyContactRelationship ?? true,
      },

      // Health insurance
      healthInsurance_boolean: {
        policyNumber_boolean: visibilitySettings.policyNumber ?? true,
        policyPeriod_boolean: visibilitySettings.policyPeriod ?? true,
        effectiveDate_boolean: visibilitySettings.effectiveDate ?? true,
        expirationDate_boolean: visibilitySettings.expirationDate ?? true,
        sumInsured_boolean: visibilitySettings.sumInsured ?? true,
      },

      // Social media
      socialMedia_boolean: {
        facebook_boolean: [visibilitySettings.facebook ?? true],
        instagram_boolean: [visibilitySettings.instagram ?? true],
        twitter_boolean: [visibilitySettings.twitter ?? true],
        linkedin_boolean: [visibilitySettings.linkedIn ?? true],
        snapchat_boolean: [visibilitySettings.snapchat ?? true],
        whatsapp_boolean: [visibilitySettings.whatsapp ?? true],
        telegram_boolean: [visibilitySettings.telegram ?? true],
        signal_boolean: [visibilitySettings.signal ?? true],
        skype_boolean: [visibilitySettings.skype ?? true],
        youtube_boolean: [visibilitySettings.youtube ?? true],
        twitch_boolean: [visibilitySettings.twitch ?? true],
        tiktok_boolean: [visibilitySettings.tiktok ?? true],
        iMessage_boolean: [visibilitySettings.iMessage ?? true],
        googleChat_boolean: [visibilitySettings.googleChat ?? true],
        discord_boolean: [visibilitySettings.discord ?? true],
        slack_boolean: [visibilitySettings.slack ?? true],
        wechat_boolean: [visibilitySettings.wechat ?? true],
        kik_boolean: [visibilitySettings.kik ?? true],
        line_boolean: [visibilitySettings.line ?? true],
      },

      // Billing address
      billing_address_boolean: [
        {
          officeBuilding_boolean: visibilitySettings.billingApartment ?? true,
          street_boolean: visibilitySettings.billingStreet ?? true,
          city_boolean: visibilitySettings.billingCity ?? true,
          state_boolean: visibilitySettings.billingState ?? true,
          postalCode_boolean: visibilitySettings.billingZipCode ?? true,
          country_boolean: visibilitySettings.billingCountry ?? true,
          billingType_boolean: true, // Default to true
        },
      ],

      // Card details
      card_details_boolean: [
        {
          nameOnCard_boolean: visibilitySettings.cardName ?? true,
          cardNumber_boolean: visibilitySettings.cardNumber ?? true,
          expiryDate_boolean: visibilitySettings.cardExpiry ?? true,
          cvv_boolean: visibilitySettings.cardCvv ?? true,
        },
      ],

      // Account details
      account_details_boolean: [
        {
          name_boolean: visibilitySettings.accountName ?? true,
          bank_boolean: visibilitySettings.bankName ?? true,
          accountNumber_boolean: visibilitySettings.accountNumber ?? true,
          ifscCode_boolean: visibilitySettings.ifscCode ?? true,
        },
      ],

      // PayPal account (default structure)
      payPal_account_boolean: [
        {
          paypalEmail_boolean: true,
          paypalLink_boolean: true,
        },
      ],

      // Crypto wallet (default structure)
      crypto_wallet_boolean: [
        {
          wallet_address_boolean: true,
          wallet_type_boolean: true,
        },
      ],

      // Recurring dates (default structure)
      recurring_dates_boolean: [
        {
          title_boolean: true,
          recurrencePattern_boolean: true,
          recurringDay_boolean: true,
          sendMethod_boolean: true,
          ecardUrl_boolean: true,
          message_boolean: true,
        },
      ],

      // Business/Company fields
      company: {
        companyInformation_boolean: {
          company_name_boolean: visibilitySettings.companyName ?? true,
          company_logo_boolean: visibilitySettings.companyLogo ?? true,
          website_boolean: visibilitySettings.companyWebsite ?? true,
          email_boolean: [
            {
              address_boolean: true,
              type_boolean: true,
              isPrimary_boolean: true,
            },
          ],
          phone_boolean: [
            {
              number_boolean: true,
              countryCode_boolean: true,
              type_boolean: true,
              isPrimary_boolean: true,
            },
          ],
          fax_boolean: [
            {
              number_boolean: true,
              countryCode_boolean: true,
            },
          ],
        },
        workContact_boolean: {
          phoneNumber_boolean: visibilitySettings.workPhone ?? true,
          phoneNumber_country_code_boolean: true,
          fax_boolean: visibilitySettings.workFax ?? true,
          email_boolean: visibilitySettings.workEmail ?? true,
        },
        companySocialMedia_boolean: {
          facebook_boolean: [visibilitySettings.facebook ?? true],
          instagram_boolean: [visibilitySettings.instagram ?? true],
          twitter_boolean: [visibilitySettings.twitter ?? true],
          linkedin_boolean: [visibilitySettings.linkedIn ?? true],
          snapchat_boolean: [visibilitySettings.snapchat ?? true],
          whatsapp_boolean: [visibilitySettings.whatsapp ?? true],
          telegram_boolean: [visibilitySettings.telegram ?? true],
          signal_boolean: [visibilitySettings.signal ?? true],
          skype_boolean: [visibilitySettings.skype ?? true],
          youtube_boolean: [visibilitySettings.youtube ?? true],
          twitch_boolean: [visibilitySettings.twitch ?? true],
          tiktok_boolean: [visibilitySettings.tiktok ?? true],
        },
        companyMessengerIds_boolean: {
          iMessage_boolean: [visibilitySettings.iMessage ?? true],
          googleChat_boolean: [visibilitySettings.googleChat ?? true],
          discord_boolean: [visibilitySettings.discord ?? true],
          slack_boolean: [visibilitySettings.slack ?? true],
          wechat_boolean: [visibilitySettings.wechat ?? true],
          kik_boolean: [visibilitySettings.kik ?? true],
          line_boolean: [visibilitySettings.line ?? true],
        },
        jobTitle_boolean: visibilitySettings.jobTitle ?? true,
        department_boolean: visibilitySettings.department ?? true,
        workSchedule_boolean: visibilitySettings.workSchedule ?? true,
        officeLocation_boolean: visibilitySettings.officeLocation ?? true,
        resume_boolean: visibilitySettings.resume ?? true,
        linkedinProfile_boolean: visibilitySettings.linkedinProfile ?? true,
        xProfile_boolean: visibilitySettings.xProfile ?? true,
        industry_boolean: visibilitySettings.industry ?? true,
        notes_boolean: [true],
        business_address_boolean: [
          {
            officeBuilding_boolean:
              visibilitySettings.businessApartment ?? true,
            street_boolean: visibilitySettings.businessStreet ?? true,
            city_boolean: visibilitySettings.businessCity ?? true,
            state_boolean: visibilitySettings.businessState ?? true,
            postalCode_boolean: visibilitySettings.businessZipCode ?? true,
            country_boolean: visibilitySettings.businessCountry ?? true,
            businessType_boolean: true,
          },
        ],
        certifications_boolean: [
          {
            url_boolean: true,
          },
        ],
        billing_address_boolean: [
          {
            officeBuilding_boolean: visibilitySettings.billingApartment ?? true,
            street_boolean: visibilitySettings.billingStreet ?? true,
            city_boolean: visibilitySettings.billingCity ?? true,
            state_boolean: visibilitySettings.billingState ?? true,
            postalCode_boolean: visibilitySettings.billingZipCode ?? true,
            country_boolean: visibilitySettings.billingCountry ?? true,
            billingType_boolean: true,
          },
        ],
        account_details_boolean: [
          {
            name_boolean: visibilitySettings.accountName ?? true,
            bank_boolean: visibilitySettings.bankName ?? true,
            accountNumber_boolean: visibilitySettings.accountNumber ?? true,
            ifscCode_boolean: visibilitySettings.ifscCode ?? true,
          },
        ],
        payPal_account_boolean: [
          {
            paypalEmail_boolean: true,
            paypalLink_boolean: true,
          },
        ],
        card_details_boolean: [
          {
            nameOnCard_boolean: visibilitySettings.cardName ?? true,
            cardNumber_boolean: visibilitySettings.cardNumber ?? true,
            expiryDate_boolean: visibilitySettings.cardExpiry ?? true,
            cvv_boolean: visibilitySettings.cardCvv ?? true,
          },
        ],
      },

      // Email and phone arrays for personal profile
      emails_boolean: [
        {
          address_boolean: true,
          type_boolean: true,
          isPrimary_boolean: true,
        },
      ],
      phoneNumbers_boolean: [
        {
          number_boolean: true,
          countryCode_boolean: true,
          type_boolean: true,
          isPrimary_boolean: true,
        },
      ],
      addresses_boolean: [
        {
          apartment_boolean: visibilitySettings.apartment ?? true,
          street_boolean: visibilitySettings.street ?? true,
          city_boolean: visibilitySettings.city ?? true,
          state_boolean: visibilitySettings.state ?? true,
          postalCode_boolean: visibilitySettings.zipCode ?? true,
          country_boolean: visibilitySettings.country ?? true,
          addressType_boolean: true,
        },
      ],
    };
  }, []);

  const collectVisibilitySettings = useCallback(
    () => ({
      ...(personalSectionRef.current?.getFieldVisibility() || {}),
      ...(socialSectionRef.current?.getFieldVisibility() || {}),
      ...(businessSectionRef.current?.getFieldVisibility() || {}),
    }),
    []
  );

  const handleNext = useCallback(async () => {
    try {
      setLoading(true);
      if (activeMainSection === "personal") {
        setActiveMainSection("social");
        tabPosition.value = withSpring(1 * styles.tabButton.width, {
          damping: 15,
          stiffness: 120,
          mass: 1.4,
        });
        console.log("Form data before submission:", form);
      } else if (activeMainSection === "social") {
        console.log("🚀 ~ handleNext ~ activeMainSection:", activeMainSection);

        // Build emails and phoneNumbers arrays
        const emails = [form.email, ...additionalEmails]
          .filter(Boolean)
          .map((address) => ({ address }));

        const phoneNumbers = [
          { number: form.phone, countryCode: "" },
          ...additionalPhones,
        ]
          .filter(Boolean)
          .map((number) => ({ number }));
        console.log("additionalEmails:", form.phone);
        console.log("additionalPhones:", additionalPhones);

        const visibilitySettings = collectVisibilitySettings();
        const mappedVisibilitySettings =
          mapVisibilitySettingsToAPI(visibilitySettings);
        const payload = {
          userInfo: {
            firstName: form.firstName,
            middleName: form.middleName,
            profile_image: form.profileUrl,
            lastName: form.lastName,
            nickname: form.nickname,
            dateOfBirth: form.dateOfBirth,
            gender: form.gender,
            nationality: form.nationality,
            maritalStatus: form.maritalStatus,
            spouseName: form.spouseName,
            emails: emails.map((email) => ({
              address: email.address,
              type: "personal",
              isPrimary: email.address === form.email,
            })),
            phoneNumbers: phoneNumbers.map((phone) => ({
              number: phone.number.number || "",
              countryCode: phone.number.countryCode || "",
              type: "personal",
              isPrimary: phone.number === form.phone,
            })),
            addresses_home: {
              apartment: form.apartment,
              street: form.street,
              city: form.city,
              state: form.state,
              postalCode: form.zipCode,
              country: form.country,
            },
            addresses: form.otherAddresses.map((addr) => ({
              apartment: addr.apartment,
              street: addr.street,
              city: addr.city,
              state: addr.state,
              postalCode: addr.zipCode,
              country: addr.country,
            })),
            languages: [form.nativeLanguage, ...form.otherLanguages],
            notes: form.notes,
            childrenName: form.childrenNames,
            hobbies: form.hobbies,
            preferredContactMethod: form.preferredContactMethod,
            timeZone: form.timezone,
            personalWebsite: form.personalWebsite,
            religion: form.religion,
            emergency_contact: {
              contactName: form.emergencyContactName,
              phoneNumber: form.emergencyPhone,
              relationship: form.emergencyContactRelationship,
              email: form.emergencyEmail,
            },
            healthInsurance: {
              policyNumber: form.policyNumber,
              policyPeriod: form.policyPeriod,
              effectiveDate: form.effectiveDate,
              expirationDate: form.expirationDate,
              sumInsured: form.sumInsured,
              insuranceProvider: form.insuranceProvider,
            },
            billing_address: form.billingAddresses.map((addr) => ({
              apartment: addr.apartment,
              street: addr.street,
              city: addr.city,
              state: addr.state,
              postalCode: addr.zipCode,
              country: addr.country,
            })),
            card_details: form.cardDetails.map((card) => ({
              nameOnCard: card.cardName,
              cardNumber: card.cardNumber,
              expiryDate: card.cardExpiry,
              cvv: card.cardCvv,
            })),
            account_details: form.accountDetails.map((acc) => ({
              name: acc.name,
              bank: acc.bank,
              accountNumber: acc.accountNumber,
              ifscCode: acc.ifscCode,
            })),
            payPal_account: form.paypalDetails.map((paypal) => ({
              paypalEmail: paypal.paypalEmail || "",
              paypalLink: paypal.paypalLink || "",
            })),
            crypto_wallet: form.cryptoWalletDetails.map((wallet) => ({
              wallet_address: wallet.walletAddress || "",
              wallet_type: wallet.walletType || "",
            })),
            socialMedia: {
              facebook: form.facebook.map((url) => ({ url: url || "" })),

              instagram: form.instagram.map((url) => ({ url: url || "" })),
              twitter: form.twitter.map((url) => ({ url: url || "" })),
              linkedin: form.linkedIn.map((url) => ({ url: url || "" })),
              snapchat: form.snapchat.map((url) => ({ url: url || "" })),
              whatsapp: form.whatsapp.map((url) => ({ url: url || "" })),
              telegram: form.telegram.map((url) => ({ url: url || "" })),
              signal: form.signal.map((url) => ({ url: url || "" })),
              skype: form.skype.map((url) => ({ url: url || "" })),
              youtube: form.youtube.map((url) => ({ url: url || "" })),
              twitch: form.twitch.map((url) => ({ url: url || "" })),
              tiktok: form.tiktok.map((url) => ({ url: url || "" })),
              iMessage: form.iMessage.map((url) => ({ url: url || "" })),
              googleChat: form.googleChat.map((url) => ({ url: url || "" })),
              discord: form.discord.map((url) => ({ url: url || "" })),
              wechat: form.wechat.map((url) => ({ url: url || "" })),
              kik: form.kik.map((url) => ({ url: url || "" })),
              line: form.line.map((url) => ({ url: url || "" })),
            },
          },
          getProfileAccess: mappedVisibilitySettings,
        };
        console.log(
          "🚀 ~ handleNext ~ payload:",
          JSON.stringify(payload, null, 2)
        );

        const profile_management_id = profileId;
        const response = await dispatch(
          updateSharingProfile({
            profile_management_id,
            profileData: payload,
          })
        );

        if (response.payload && response.payload.success) {
          showToast("success", "Profile submitted.");
          setActiveMainSection("business");
          tabPosition.value = withSpring(2 * styles.tabButton.width, {
            damping: 15,
            stiffness: 120,
            mass: 1.4,
          });
        } else {
          Alert.alert("Error", "Failed to save social information");
        }
      } else if (activeMainSection === "business") {
        const visibilitySettings = collectVisibilitySettings();
        const mappedVisibilitySettings =
          mapVisibilitySettingsToAPI(visibilitySettings);
        // Construct the business details payload as per user requirements
        const businessPayload = {
          professionalInfo: {
            jobTitle: form.jobTitle || "",
            department: form.department || "",
            industry: form.industry || "",
            workSchedule: form.workHours || "",
            notes: form.professionalNotes || "",

            account_details: (form.companyInfo[0]?.bankAccountInfo || []).map(
              (acc) => ({
                name: acc.accountName || "",
                bank: acc.bankName || "",
                accountNumber: acc.accountNumber || "",
                ifscCode: acc.ifscCode || "",
              })
            ),

            billing_address: (
              form.companyInfo[0]?.companyBillingAddresses || []
            ).map((addr) => ({
              street: addr.street || "",
              city: addr.city || "",
              state: addr.state || "",
              postalCode: addr.zipCode || "",
              country: addr.country || "",
            })),

            business_address: (form.companyInfo[0]?.companyAddresses || []).map(
              (addr) => ({
                street: addr.street || "",
                city: addr.city || "",
                state: addr.state || "",
                postalCode: addr.zipCode || "",
                country: addr.country || "",
              })
            ),

            card_details: (form.companyInfo[0]?.cardDetails || []).map(
              (card) => ({
                nameOnCard: card.cardName || "",
                cardNumber: card.cardNumber || "",
                expiryDate: card.cardExpiry || "",
                cvv: card.cardCvv || "",
              })
            ),

            certifications: (form.certifications || []).map((cert) => ({
              url: cert || "",
            })),

            companyInformation: {
              company_name: form.company || "as",
              company_logo: form.companyInfo[0]?.companyLogo || "",

              email: (form.companyInfo[0]?.companyEmails || []).map(
                (email, index) => ({
                  address: email || "",
                  type: "work",
                  isPrimary: index === 0,
                })
              ),

              phone: (form.companyInfo[0]?.companyPhoneNumbers || []).map(
                (phone, index) => ({
                  number: phone || "",
                  countryCode: "",
                  type: "work",
                  isPrimary: index === 0,
                })
              ),

              fax: (form.companyInfo[0]?.companyFaxNumbers || []).map(
                (fax, index) => ({
                  number: fax || "",
                  countryCode: "",
                  type: "work",
                  isPrimary: index === 0,
                })
              ),

              website: form?.companyInfo[0]?.companyWebsite || "",
            },

            companyMessengerIds: {
              iMessage: (
                form?.companyInfo[0]?.companySocialMedia?.iMessage || []
              ).map((url) => ({ url: url || "" })),
              googleChat: (
                form?.companyInfo[0]?.companySocialMedia?.googleChat || []
              ).map((url) => ({ url: url || "" })),
              discord: (
                form?.companyInfo[0]?.companySocialMedia?.discord || []
              ).map((url) => ({ url: url || "" })),
              wechat: (
                form?.companyInfo[0]?.companySocialMedia?.wechat || []
              ).map((url) => ({ url: url || "" })),
              kik: (form?.companyInfo[0]?.companySocialMedia?.kik || []).map(
                (url) => ({ url: url || "" })
              ),
              line: (form?.companyInfo[0]?.companySocialMedia?.line || []).map(
                (url) => ({ url: url || "" })
              ),
            },

            companySocialMedia: {
              facebook: (
                form?.companyInfo[0]?.companySocialMedia?.facebook || []
              ).map((url) => ({ url: url || "" })),
              instagram: (
                form?.companyInfo[0]?.companySocialMedia?.instagram || []
              ).map((url) => ({ url: url || "" })),
              twitter: (
                form?.companyInfo[0]?.companySocialMedia?.twitter || []
              ).map((url) => ({ url: url || "" })),
              linkedin: (
                form?.companyInfo[0]?.companySocialMedia?.linkedIn || []
              ).map((url) => ({ url: url || "" })),
              snapchat: (
                form?.companyInfo[0]?.companySocialMedia?.snapchat || []
              ).map((url) => ({ url: url || "" })),
              whatsapp: (
                form?.companyInfo[0]?.companySocialMedia?.whatsapp || []
              ).map((url) => ({ url: url || "" })),
              telegram: (
                form?.companyInfo[0]?.companySocialMedia?.telegram || []
              ).map((url) => ({ url: url || "" })),
              signal: (
                form?.companyInfo[0]?.companySocialMedia?.signal || []
              ).map((url) => ({ url: url || "" })),
              skype: (
                form?.companyInfo[0]?.companySocialMedia?.skype || []
              ).map((url) => ({ url: url || "" })),
              youtube: (
                form?.companyInfo[0]?.companySocialMedia?.youtube || []
              ).map((url) => ({ url: url || "" })),
              twitch: (
                form?.companyInfo[0]?.companySocialMedia?.twitch || []
              ).map((url) => ({ url: url || "" })),
              tiktok: (
                form?.companyInfo[0]?.companySocialMedia?.tiktok || []
              ).map((url) => ({ url: url || "" })),
            },

            contactId: user?._id || "",
            officeLocation: form.officeBuilding || "",
            resume: form.resume || "",
            status: "active",
            userId: user?._id || "",

            workContact: {
              phoneNumber: form.workPhone || "",
              fax: form.workFax || "",
              email: form.workEmail || "",
            },

            xProfile: form.businessTwitter || "",
            linkedinProfile: form.businessLinkedIn || "",
          },

          privacySettings: mappedVisibilitySettings,
        };
        console.log("Business Profile Data:", JSON.stringify(businessPayload));

        // return;

        const profile_management_id = profileId;
        console.log("here", profile_management_id);
        console.log(
          "🚀 ~ handleNext ~ profile_management_id:",
          profile_management_id
        );

        const response = await dispatch(
          updateBusinessDetailsSharingProfile({
            profile_management_id,
            profileData: businessPayload,
          })
        );
        console.log("🚀 ~ handleNext ~ response:", JSON.stringify(response));

        if (response.payload && response.payload.success) {
          Alert.alert("Success", "Profile completed successfully");
          navigation.navigate("MainApp");
        } else {
          Alert.alert("Error", "Failed to complete profile");
        }
      }
    } catch (error) {
      console.error("Error in profile completion:", error);
      Alert.alert("Error", "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  }, [
    activeMainSection,
    collectVisibilitySettings,
    mapVisibilitySettingsToAPI,
    dispatch,
    form,
    navigation,
    tabPosition,
    user,
  ]);

  return (
    <View style={styles.container}>
      <Header
        title={ownProfileData ? "Edit your Profile" : "Complete Your Profile"}
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />
      <TabNavigation
        activeSection={activeMainSection}
        tabPosition={animatedStyle}
        handleTabPress={handleTabPress}
        mainSections={MAIN_SECTIONS}
      />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "height" : null}
        keyboardVerticalOffset={0}
      >
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={[
            styles.formContainer,
            isTimezoneDropdownOpen && { paddingBottom: 200 },
          ]}
          showsVerticalScrollIndicator={false}
        >
          <ProfilePicture
            uri={form.profileUrl}
            onUploadSuccess={(url) =>
              setForm((prev) => ({ ...prev, profileUrl: url }))
            }
          />
          {activeMainSection === "personal" && (
            <PersonalSection
              ref={personalSectionRef}
              form={form}
              handleChange={handleChange}
              errors={errors}
              setErrors={setErrors}
              phoneInputRef={phoneInputRef}
              ownProfileData={ownProfileData}
              onTimezoneDropdownToggle={setIsTimezoneDropdownOpen}
              additionalEmails={additionalEmails}
              setAdditionalEmails={setAdditionalEmails}
              additionalPhones={additionalPhones}
              setAdditionalPhones={setAdditionalPhones}
            />
          )}
          {activeMainSection === "social" && (
            <SocialSection
              ref={socialSectionRef}
              form={form}
              handleChange={handleChange}
              errors={errors}
              ownProfileData={ownProfileData}
            />
          )}
          {activeMainSection === "business" && (
            <BusinessSection
              ref={businessSectionRef}
              form={form}
              handleChange={handleChange}
              errors={errors}
              ownProfileData={ownProfileData}
            />
          )}
          <PrimaryButton
            onPress={handleNext}
            title="Next"
            style={[styles.nextButton]}
            loading={loading}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

ProfileCompletionScreen.propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func.isRequired,
    goBack: PropTypes.func.isRequired,
  }).isRequired,
};

export default ProfileCompletionScreen;
