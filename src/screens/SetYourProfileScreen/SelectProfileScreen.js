import React, { useState, useEffect, useCallback } from "react";
import {
  Image,
  StyleSheet,
  View,
  TouchableOpacity,
  ScrollView,
  FlatList,
} from "react-native";
import { useSelector, useDispatch } from "react-redux";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import AppLoader from "../../components/AppLoader";
import MyText from "../../components/MyText";
import commonStyles from "../../assets/commonStyles";
import ChipSelector from "../../components/ChipSelector";
import colors from "../../assets/colors";
import InputField from "../../components/InputField";
import { PrimaryButton } from "../../components/Button";
import { setProfileContactsData } from "../../redux/features/mainSlice";
import { storeProfile } from "../../redux/features/SharingProfileSlice";
import { showToast } from "../../utils/toastConfig";
import BottomModal from "../../components/BottomModal";
import ContactCard from "../../components/ContactCard";

const PROFILE_OPTIONS = [
  {
    label: "Home",
    value: "home",
    icon: <Image source={icons.HomeIconProfile} resizeMode="contain" />,
  },
  {
    label: "Work",
    value: "work",
    icon: <Image source={icons.WorkIconProfile} resizeMode="contain" />,
  },
  {
    label: "Family",
    value: "family",
    icon: <Image source={icons.FamilyIconProfile} resizeMode="contain" />,
  },
  {
    label: "Friends",
    value: "friends",
    icon: <Image source={icons.FriendsIconProfile} resizeMode="contain" />,
  },
];

const getMatchingOption = (text) =>
  PROFILE_OPTIONS.find(
    (option) => option.label.toLowerCase() === text.toLowerCase()
  );

const SelectProfileScreen = ({ navigation, route }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState("home");
  const [profileName, setProfileName] = useState("Home");
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [errors, setErrors] = useState({ profileName: "", contacts: "" });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [savedProfileId, setSavedProfileId] = useState(null);

  const dispatch = useDispatch();
  const storeProfileState = useSelector(
    (state) => state.sharingProfileSlice.storeProfile
  );
  const { data, loading, error } = storeProfileState;
  const profileContactsData = useSelector(
    (state) => state.mainSlice.profileContactsData
  );

  // Sync selected contacts from redux
  useEffect(() => {
    setSelectedContacts(profileContactsData || []);
  }, [profileContactsData]);

  // Clear contacts on tab press
  useEffect(() => {
    const unsubscribe = navigation.addListener("tabPress", () => {
      dispatch(setProfileContactsData([]));
    });
    return unsubscribe;
  }, [navigation, dispatch]);

  // Update profile name when tab changes
  useEffect(() => {
    const selectedOption = PROFILE_OPTIONS.find(
      (option) => option.value === selectedTab
    );
    if (selectedOption) setProfileName(selectedOption.label);
  }, [selectedTab]);

  // Handlers
  const handleProfileNameChange = useCallback((text) => {
    setProfileName(text);
    const matchingOption = getMatchingOption(text);
    setSelectedTab(matchingOption ? matchingOption.value : "");
    setErrors((prev) => ({
      ...prev,
      profileName: text.trim() === "" ? "Profile name is required" : "",
    }));
  }, []);

  const handleCreateNewProfile = useCallback(() => {
    setProfileName("");
    setSelectedTab("");
  }, []);

  const navigateToContactSelection = useCallback(() => {
    setErrors({ ...errors, contacts: "" });
    navigation.navigate("AddContactsScreen", { fromProfile: true });
  }, [navigation]);

  const validateForm = () => {
    let isValid = true;
    const newErrors = { ...errors };
    if (profileName.trim() === "") {
      newErrors.profileName = "Profile name is required";
      isValid = false;
    }
    if (selectedContacts.length === 0) {
      newErrors.contacts = "Please select at least one contact";
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const handleSaveProfile = async () => {
    if (!validateForm()) return;
    setIsLoading(true);
    const apiBody = {
      profile_name: profileName,
      addInProfile: selectedContacts.map((c) => ({ memberId: c.id })),
    };
    try {
      const response = await dispatch(storeProfile(apiBody)).unwrap();
      console.log(
        "🚀 ~ handleSaveProfile ~ response:",
        JSON.stringify(response, null, 2)
      );
      setIsLoading(false);
      const isSuccess =
        response.success || (response.payload && response.payload.success);
      if (isSuccess) {
        showToast(
          "success",
          response?.payload?.message ||
            response?.message ||
            "Contacts added successfully."
        );
        // navigation.replace("ProfileCompletionScreen", {
        //   profileId: response?.data?._id,
        // });
        setSavedProfileId(response?.data?._id);
        setIsModalVisible(true);
      } else {
        console.log("i m in else ");
        showToast(
          "error",
          response?.payload?.message ||
            response?.message ||
            "Failed to add contacts."
        );
      }
    } catch (e) {
      showToast(
        "error",
        e?.payload?.message || e?.message || "Failed to add contacts."
      );
      setIsLoading(false);
    }
  };
  const handleModalClose = () => {
    setIsModalVisible(false);
    if (savedProfileId) {
      navigation.replace("ProfileCompletionScreen", {
        profileId: savedProfileId,
      });
    }
  };
  const handleSeeAllProfiles = () => {
    setIsModalVisible(false);
    setTimeout(() => {
      navigation.goBack();
    }, 800);
  };

  return (
    <View style={{ flex: 1, backgroundColor: colors.white }}>
      <Header
        title="Set Your Profile"
        leftIcon={icons?.backwBg}
        onPressLeft={() => navigation.goBack()}
        pb={20}
        textCenter
      />
      <AppLoader isLoading={isLoading} />
      <View style={{ flex: 1 }}>
        <View style={styles.contentContainer}>
          <MyText h6 semibold>
            Select Profile
          </MyText>
          <View style={commonStyles.row}>
            <ChipSelector
              options={PROFILE_OPTIONS}
              selectedValue={selectedTab}
              onSelect={setSelectedTab}
              containerStyle={styles.chipContainer}
            />
          </View>
          <View style={{ marginTop: 10 }}>
            <InputField
              label="Profile Name*"
              value={profileName}
              onChangeText={handleProfileNameChange}
              error={errors.profileName}
              placeholder="Enter profile name"
              editable={!getMatchingOption(profileName)}
            />
            <MyText
              p
              medium
              underline
              style={{ alignSelf: "flex-end" }}
              onPress={handleCreateNewProfile}
            >
              +Create New Profile
            </MyText>
            {/* <InputField
              label="Select Contacts"
              value={
                selectedContacts.length > 0
                  ? `${selectedContacts.length} contacts selected`
                  : ""
              }
              editable={false}
              error={errors.contacts}
              placeholder="Tap to select contacts"
              rightIcon={icons.rightArrow}
              onPress={navigateToContactSelection}
            /> */}
            <MyText p medium style={styles.label}>
              Select Contacts
            </MyText>
            <TouchableOpacity
              onPress={navigateToContactSelection}
              style={{
                flexDirection: "row",
                alignItems: "center",
                backgroundColor: "#f2f2f2",
                borderRadius: 10,
                paddingHorizontal: 12,
                position: "relative",
              }}
            >
              <View
                style={{
                  flex: 1,
                  height: 48,
                  justifyContent: "center",
                }}
              >
                <MyText p color={"#7D7D7D"}>
                  Tap to select contacts
                </MyText>
              </View>
            </TouchableOpacity>
            {/* Show selected contacts as chips */}
            {/* {selectedContacts.length > 0 && (
              <View
                style={{
                  flexDirection: "row",
                  flexWrap: "wrap",
                  marginTop: 10,
                }}
              > */}
            {/* {selectedContacts.map((contact) => (
                  <View
                    key={contact.id}
                    style={{
                      backgroundColor: colors.primary || "#e0e0e0",
                      borderRadius: 16,
                      paddingHorizontal: 12,
                      paddingVertical: 6,
                      marginRight: 8,
                      marginBottom: 8,
                      paddingRight: 26,
                    }}
                  >
                    <MyText style={{ color: "#fff", fontSize: 14 }}>
                      {contact.name}
                    </MyText> */}
            {/* cross button to remove it */}
            {/* <TouchableOpacity
                      onPress={() => {
                        setSelectedContacts((prev) =>
                          prev.filter((c) => c.id !== contact.id)
                        );

                        const updatedContacts = selectedContacts.filter(
                          (c) => c.id !== contact.id
                        );
                        dispatch(setProfileContactsData(updatedContacts));
                      }}
                      style={{
                        position: "absolute",
                        right: 8,
                        top: 8,
                      }}
                    >
                      <Image
                        source={icons.closeIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    </TouchableOpacity>
                  </View>
                ))} */}
            {/* </View>
            )} */}
            <View style={{ height: "68%" }}>
              <FlatList
                data={selectedContacts}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 80 }}
                renderItem={({ item }) => (
                  <ContactCard
                    name={item.name}
                    phone={item.phone}
                    mode={"cancel"}
                    onCancel={() => {
                      setSelectedContacts((prev) =>
                        prev.filter((c) => c.id !== item.id)
                      );

                      const updatedContacts = selectedContacts.filter(
                        (c) => c.id !== item.id
                      );
                      dispatch(setProfileContactsData(updatedContacts));
                    }}
                    imgUrl={item.imgUrl || ""}
                  />
                )}
                // onPress=
                keyExtractor={(item) => item.id}
                // ListFooterComponent={() => {
                //   selectedContacts.length > 0 && (
                //     <MyText
                //       underline
                //       medium
                //       h7
                //       children={"+Add More"}
                //       style={{ marginTop: 12, alignSelf: "flex-end" }}
                //       onPress={navigateToContactSelection}
                //     />
                //   );
                // }}
              />
            </View>
          </View>
        </View>
      </View>
      <PrimaryButton
        title="Save Profile"
        style={{ marginTop: 30, bottom: 30 }}
        onPress={handleSaveProfile}
      />
      <BottomModal
        isVisible={isModalVisible}
        onClose={handleModalClose}
        title="Profile Saved"
      >
        <View style={{ padding: 16 }}>
          <Image
            source={icons.successIcon}
            style={{ width: 80, height: 80, alignSelf: "center" }}
            resizeMode="cover"
          />
          <MyText p medium style={{ textAlign: "center", marginTop: 20 }}>
            Your profile has been created successfully.
          </MyText>
          <PrimaryButton
            title="Go to profile"
            style={{ marginTop: 20 }}
            // onPress={handleModalClose}
            onPress={handleSeeAllProfiles}
          />
          <PrimaryButton
            title="Continue sharing profile"
            style={{ marginTop: 20 }}
            onPress={handleModalClose}
          />
        </View>
      </BottomModal>
    </View>
  );
};

export default SelectProfileScreen;

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    padding: 20,
  },
  chipContainer: {
    width: "100%",
    gap: 10,
  },
  label: { marginBottom: 6 },
});
